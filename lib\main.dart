import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:media_kit/media_kit.dart';
import 'firebase_options.dart';
import 'services/firebase_config.dart';
import 'controllers/auth_controller.dart';
import 'controllers/language_controller.dart';
import 'controllers/movie_controller.dart';
import 'controllers/favorite_controller.dart';
import 'controllers/ticket_controller.dart';
import 'controllers/booking_controller.dart';
import 'controllers/schedule_controller.dart';
import 'controllers/banner_controller.dart';
import 'controllers/realtime_notification_controller.dart';
import 'controllers/realtime_bug_report_controller.dart';
import 'bindings/realtime_database_binding.dart';
import 'services/reservation_cleanup_service.dart';
import 'services/ticket_expiration_service.dart';
import 'utils/developer_mode.dart';
import 'utils/app_colors.dart';
import 'translations/app_translations.dart';
import 'view/page/auth/login_page.dart';
import 'view/page/splash_screen.dart';
import 'view/page/profile_edit_page.dart';
import 'view/page/bug_report_detail_page.dart';
import 'view/page/notification_page.dart';
import 'view/page/notification_settings_page.dart';
import 'view/page/realtime_notification_tabs_page.dart';
import 'view/page/realtime_bug_report_detail_page.dart';
import 'view/page/search_page.dart';
import 'view/page/all_movies_page.dart';
import 'view/root_page.dart';
import 'view/admin/admin_routes.dart';
import 'debug_firestore.dart';
import 'debug_add_banner.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize MediaKit
  MediaKit.ensureInitialized();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize Firebase first
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Configure Firebase settings to reduce warnings
  FirebaseConfig.configureFirebaseLocale();

  // Initialize Firebase App Check (optional, helps reduce warnings)
  await FirebaseConfig.initializeAppCheck();

  // Configure Firebase Auth
  await FirebaseConfig.configureFirebaseAuth();

  // Initialize all controllers immediately but efficiently
  Get.put(AuthController());
  Get.put(LanguageController());
  Get.put(MovieController());
  Get.put(FavoriteController());
  Get.put(TicketController());
  Get.put(BookingController());
  Get.put(ScheduleController());
  Get.put(BannerController());
  Get.put(RealtimeNotificationController());
  Get.put(RealtimeBugReportController());
  Get.put(DeveloperMode());

  // Initialize reservation cleanup service
  Get.put(ReservationCleanupService());

  // Initialize ticket expiration service
  Get.put(TicketExpirationService());

  // Initialize bindings
  RealtimeDatabaseBinding().dependencies();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final languageController = Get.find<LanguageController>();

    return GetMaterialApp(
      title: 'Đớp Phim',
      debugShowCheckedModeBanner: false,

      // Translations
      translations: AppTranslations(),
      locale: languageController.currentLocale,
      fallbackLocale: const Locale('en', 'US'),

      // Routes
      getPages: [
        ...AdminRoutes.routes,
        GetPage(
          name: '/debug',
          page: () => const FirestoreDebugPage(),
        ),
        GetPage(
          name: '/debug/add-banner',
          page: () => const AddBannerDebugPage(),
        ),
        GetPage(
          name: '/profile/edit',
          page: () => const ProfileEditPage(),
        ),
        GetPage(
          name: '/search',
          page: () => SearchPage(
            initialQuery: Get.parameters['query'],
          ),
        ),
        GetPage(
          name: '/all_movies',
          page: () => AllMoviesPage(
            genre: Get.parameters['genre'],
          ),
        ),
        GetPage(
          name: '/bug_report_detail',
          page: () {
            // Kiểm tra xem có tham số bugReportId không
            final bugReportId = Get.parameters['bugReportId'];

            // Nếu có tham số bugReportId và có tiền tố 'rt_', sử dụng trang Realtime
            if (bugReportId != null && bugReportId.startsWith('rt_')) {
              final realBugReportId =
                  bugReportId.substring(3); // Bỏ tiền tố 'rt_'
              return RealtimeBugReportDetailPage(bugReportId: realBugReportId);
            }

            // Ngược lại, sử dụng trang thông thường
            return const BugReportDetailPage();
          },
          binding: RealtimeDatabaseBinding(),
        ),
        GetPage(
          name: '/notifications',
          page: () => const NotificationPage(
              showBackButton:
                  true), // Hiển thị nút back khi navigate từ màn khác
          binding: RealtimeDatabaseBinding(),
        ),
        GetPage(
          name: '/realtime_notifications',
          page: () => const RealtimeNotificationTabsPage(),
          binding: RealtimeDatabaseBinding(),
        ),
        GetPage(
          name: '/realtime_bug_report_detail/:bugReportId',
          page: () => RealtimeBugReportDetailPage(
            bugReportId: Get.parameters['bugReportId'] ?? '',
          ),
          binding: RealtimeDatabaseBinding(),
        ),
        GetPage(
          name: '/notification_settings',
          page: () => const NotificationSettingsPage(),
          binding: RealtimeDatabaseBinding(),
        ),
      ],
      initialRoute: '/',

      theme: ThemeData(
        primarySwatch: Colors.blue,
        brightness: Brightness.dark,
        scaffoldBackgroundColor: AppColors.scaffoldBackground,
        cardColor: AppColors.cardBackground,
        primaryColor: AppColors.primaryBlue,
        colorScheme: const ColorScheme.dark(
          primary: AppColors.primaryBlue,
          secondary: AppColors.primaryAmber,
          surface: AppColors.surfaceColor,
          error: AppColors.errorRed,
          onPrimary: AppColors.textPrimary,
          onSecondary: AppColors.textPrimary,
          onSurface: AppColors.textPrimary,
          onError: AppColors.textPrimary,
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.transparent,
          elevation: 0,
          foregroundColor: AppColors.textPrimary,
          iconTheme: IconThemeData(color: AppColors.textPrimary),
          titleTextStyle: TextStyle(
            color: AppColors.textPrimary,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.buttonPrimary,
            foregroundColor: AppColors.textPrimary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: AppColors.primaryBlue,
          ),
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            foregroundColor: AppColors.textPrimary,
            side: const BorderSide(color: AppColors.borderSecondary),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        ),
        inputDecorationTheme: const InputDecorationTheme(
          labelStyle: TextStyle(color: AppColors.textSecondary),
          hintStyle: TextStyle(color: AppColors.textTertiary),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppColors.borderPrimary),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppColors.borderAccent),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppColors.errorRed),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppColors.errorRed),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
        ),
        chipTheme: const ChipThemeData(
          backgroundColor: AppColors.surfaceColor,
          labelStyle: TextStyle(color: AppColors.textPrimary),
          selectedColor: AppColors.primaryBlue,
          disabledColor: AppColors.textDisabled,
        ),
        snackBarTheme: const SnackBarThemeData(
          backgroundColor: AppColors.surfaceColor,
          contentTextStyle: TextStyle(color: AppColors.textPrimary),
        ),
      ),
      home: const AuthCheckPage(),
    );
  }
}

class AuthCheckPage extends StatefulWidget {
  const AuthCheckPage({Key? key}) : super(key: key);

  @override
  State<AuthCheckPage> createState() => _AuthCheckPageState();
}

class _AuthCheckPageState extends State<AuthCheckPage> {
  final bool _showSplash = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();

    if (_showSplash) {
      return const SplashScreen(
        nextScreen: LoginPage(),
      );
    }

    return _buildAuthScreen(authController);
  }

  Widget _buildAuthScreen(AuthController authController) {
    return Obx(() {
      if (authController.isLoading) {
        return const Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        );
      } else if (authController.isLoggedIn) {
        return RootPage(i: 0);
      } else {
        return const LoginPage();
      }
    });
  }
}
